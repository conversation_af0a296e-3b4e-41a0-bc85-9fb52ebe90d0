import { useState, useEffect, useRef } from 'react'
import './App.css'
import Background3D from './components/Background3D'

function App() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [openFAQ, setOpenFAQ] = useState(null)
  const [heroOpacity, setHeroOpacity] = useState(1)
  const [statementOpacity, setStatementOpacity] = useState(0)
  const heroRef = useRef(null)
  const statementRef = useRef(null)

  const toggleFAQ = (index) => {
    setOpenFAQ(openFAQ === index ? null : index)
  }

  // Team logos data
  const teamLogos = [
    { src: "/team_background_images/National University of Singapore.png", alt: "National University of Singapore", color: "blue" },
    { src: "/team_background_images/National Taiwan University.png", alt: "National Taiwan University", color: "green" },
    { src: "/team_background_images/IIIT Delhi.png", alt: "IIIT Delhi", color: "purple" },
    { src: "/team_background_images/Indian Institute of Science.png", alt: "Indian Institute of Science", color: "pink" },
    { src: "/team_background_images/Samsung Research.png", alt: "Samsung Research", color: "yellow", scale: true },
    { src: "/team_background_images/Hyperverge.png", alt: "Hyperverge", color: "indigo", scale: true },
    { src: "/team_background_images/University of South Carolina.png", alt: "University of South Carolina", color: "teal" }
  ]

  // Smooth continuous carousel - no JavaScript needed, pure CSS animation

  // Scroll-based fade transitions (faster)
  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY
      const windowHeight = window.innerHeight

      // Hero section fade out as we scroll down (faster fade)
      const heroFadeStart = windowHeight * 0.2
      const heroFadeEnd = windowHeight * 0.6
      let heroOpacityValue = 1

      if (scrollY > heroFadeStart) {
        const fadeProgress = (scrollY - heroFadeStart) / (heroFadeEnd - heroFadeStart)
        heroOpacityValue = Math.max(0, 1 - fadeProgress)
      }

      // Statement section fade in as we approach it (faster fade)
      const statementFadeStart = windowHeight * 0.4
      const statementFadeEnd = windowHeight * 0.8
      const statementFadeOutStart = windowHeight * 1.4
      const statementFadeOutEnd = windowHeight * 1.8
      let statementOpacityValue = 0

      if (scrollY > statementFadeStart && scrollY < statementFadeOutStart) {
        if (scrollY < statementFadeEnd) {
          const fadeProgress = (scrollY - statementFadeStart) / (statementFadeEnd - statementFadeStart)
          statementOpacityValue = Math.min(1, fadeProgress)
        } else {
          statementOpacityValue = 1
        }
      } else if (scrollY >= statementFadeOutStart) {
        const fadeProgress = (scrollY - statementFadeOutStart) / (statementFadeOutEnd - statementFadeOutStart)
        statementOpacityValue = Math.max(0, 1 - fadeProgress)
      }

      setHeroOpacity(heroOpacityValue)
      setStatementOpacity(statementOpacityValue)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <div className="min-h-screen w-full relative">
      {/* Three.js Background */}
      <Background3D />

      {/* Header */}
      <header className="bg-black/20 backdrop-blur-sm sticky top-0 z-50 w-full relative">
        <nav className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent hover:scale-105 transition-transform duration-300 cursor-pointer">
                Qreate AI
              </h1>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex space-x-8">
              <a href="#about" className="text-gray-300 hover:text-white transition-all duration-300 hover:scale-105 relative group">
                About Us
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#services" className="text-gray-300 hover:text-white transition-all duration-300 hover:scale-105 relative group">
                Services
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#team" className="text-gray-300 hover:text-white transition-all duration-300 hover:scale-105 relative group">
                Team
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#faqs" className="text-gray-300 hover:text-white transition-all duration-300 hover:scale-105 relative group">
                FAQs
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#contact" className="text-gray-300 hover:text-white transition-all duration-300 hover:scale-105 relative group">
                Contact Us
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="text-gray-300 hover:text-white transition-colors duration-300"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden animate-fade-in">
              <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-800/50 backdrop-blur-sm rounded-lg">
                <a href="#about" className="block px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded transition-all duration-300">About Us</a>
                <a href="#services" className="block px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded transition-all duration-300">Services</a>
                <a href="#team" className="block px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded transition-all duration-300">Team</a>
                <a href="#faqs" className="block px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded transition-all duration-300">FAQs</a>
                <a href="#contact" className="block px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded transition-all duration-300">Contact Us</a>
              </div>
            </div>
          )}
        </nav>
      </header>

      {/* Hero Section - First Page */}
      <section
        ref={heroRef}
        className="relative min-h-screen flex items-start justify-center pt-32 md:pt-40 lg:pt-48 overflow-hidden transition-opacity duration-500 ease-in-out"
        style={{ opacity: heroOpacity }}
      >
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-black/30 backdrop-blur-[1px]"></div>

        <div className="relative w-full px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-6xl md:text-7xl font-bold text-white mb-8 animate-fade-in-up">
            We help you build<br />
            <span className="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent animate-gradient-x">
              really cool stuff
            </span>
          </h1>
          <p className="text-xl text-gray-300 mb-12 max-w-3xl mx-auto animate-fade-in-up delay-300">
            Qreate AI is an AI-native product studio building fullstack AI/LLM apps
          </p>
          <button className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-4 px-10 rounded-xl text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/25 animate-fade-in-up delay-500 rocket-boost">
            <span className="flex items-center">
              Book a Call
              <svg className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </span>
          </button>
        </div>
      </section>

      {/* Company Statement Section - Second Page */}
      <section
        ref={statementRef}
        className="relative min-h-screen flex items-center justify-center overflow-hidden transition-opacity duration-500 ease-in-out"
        style={{ opacity: statementOpacity }}
      >
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-black/20 backdrop-blur-[1px]"></div>

        <div className="relative w-full px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-relaxed max-w-6xl mx-auto">
            <span className="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">
              Here at Qreate AI
            </span>
            {" "}we engineer AI native solutions. We are committed to creating innovative and inspiring world-class products. Come transform your ideas into powerful realities
          </h2>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-24 relative">
        <div className="absolute inset-0 bg-black/40 backdrop-blur-[1px]"></div>
        <div className="relative w-full px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-5xl font-bold text-white mb-6">Services</h2>
            <p className="text-2xl text-gray-300 mb-4">What We Offer</p>
            <p className="text-gray-400 text-lg">From AI/ML solutions to full-stack development - comprehensive services for modern applications</p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* AI Services */}
            <div className="group bg-gradient-to-br from-blue-900/30 to-blue-800/30 backdrop-blur-sm rounded-2xl border border-blue-500/30 p-8 hover:border-blue-400/60 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/20">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-all duration-300 shadow-lg shadow-blue-500/30">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-white mb-6 group-hover:text-blue-400 transition-colors duration-300">AI/ML Services</h3>

              <div className="space-y-4">
                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-blue-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-blue-400 mb-2">LLM Fine-tuning</h4>
                  <p className="text-gray-300 text-sm">Custom model training for specific domains and use cases</p>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-blue-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-green-400 mb-2">Computer Vision</h4>
                  <p className="text-gray-300 text-sm">Object detection, image classification, and visual AI solutions</p>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-blue-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-purple-400 mb-2">NLP & Text Processing</h4>
                  <p className="text-gray-300 text-sm">Sentiment analysis, text generation, and language understanding</p>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-blue-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-pink-400 mb-2">Audio/Speech AI</h4>
                  <p className="text-gray-300 text-sm">Speech recognition, synthesis, and audio processing</p>
                </div>
              </div>
            </div>

            {/* Development Stack */}
            <div className="group bg-gradient-to-br from-purple-900/30 to-purple-800/30 backdrop-blur-sm rounded-2xl border border-purple-500/30 p-8 hover:border-purple-400/60 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/20">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-all duration-300 shadow-lg shadow-purple-500/30">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-white mb-6 group-hover:text-purple-400 transition-colors duration-300">Development Stack</h3>

              <div className="space-y-4">
                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-purple-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-blue-400 mb-2">Frontend</h4>
                  <p className="text-gray-300 text-sm">React, JSX with Vite, Modern UI/UX</p>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-purple-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-green-400 mb-2">Backend & API</h4>
                  <p className="text-gray-300 text-sm">FastAPI, Python, JavaScript, RESTful APIs</p>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-purple-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-yellow-400 mb-2">Database</h4>
                  <p className="text-gray-300 text-sm">Firebase, Redis, Data management</p>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-purple-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-pink-400 mb-2">Cloud & Deployment</h4>
                  <p className="text-gray-300 text-sm">GCP, AWS, Netlify, Render</p>
                </div>
              </div>
            </div>

            {/* Specialized Solutions */}
            <div className="group bg-gradient-to-br from-green-900/30 to-green-800/30 backdrop-blur-sm rounded-2xl border border-green-500/30 p-8 hover:border-green-400/60 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-green-500/20">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-all duration-300 shadow-lg shadow-green-500/30">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-white mb-6 group-hover:text-green-400 transition-colors duration-300">Specialized Solutions</h3>

              <div className="space-y-4">
                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-green-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-blue-400 mb-2">AI Chatbots</h4>
                  <p className="text-gray-300 text-sm">Intelligent conversational AI for customer support</p>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-green-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-purple-400 mb-2">Data Analytics</h4>
                  <p className="text-gray-300 text-sm">ETL pipelines, data visualization, and insights</p>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-green-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-yellow-400 mb-2">Automation</h4>
                  <p className="text-gray-300 text-sm">Workflow automation and process optimization</p>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-green-500/30 transition-all duration-300">
                  <h4 className="text-lg font-semibold text-pink-400 mb-2">Custom AI Models</h4>
                  <p className="text-gray-300 text-sm">Tailored AI solutions for specific business needs</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>







      {/* Team Section */}
      <section id="team" className="py-24 relative overflow-hidden">
        {/* Background overlay */}
        <div className="absolute inset-0 bg-black/30 backdrop-blur-[1px]"></div>

        <div className="relative w-full px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-5xl font-bold text-white mb-6">Team</h2>
            <p className="text-2xl text-gray-300 mb-4">Our Background</p>
            <p className="text-gray-400 text-lg">Bringing together expertise from world-class institutions and leading tech companies</p>
          </div>

          {/* Smooth Continuous Moving Carousel */}
          <div className="relative overflow-hidden py-8">
            <div className="flex space-x-12 animate-scroll-smooth">
              {/* Render logos multiple times for seamless infinite loop */}
              {[...teamLogos, ...teamLogos, ...teamLogos, ...teamLogos].map((logo, index) => (
                <div
                  key={`${logo.alt}-${index}`}
                  className={`group flex-shrink-0 w-72 h-36 bg-gradient-to-br from-gray-700/30 to-gray-800/30 backdrop-blur-sm rounded-2xl border border-gray-600/20 flex items-center justify-center p-6 hover:border-${logo.color}-400/50 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-${logo.color}-400/10`}
                >
                  <img
                    src={logo.src}
                    alt={logo.alt}
                    className={`max-w-full max-h-full object-contain filter brightness-90 group-hover:brightness-110 transition-all duration-300 ${logo.scale ? 'scale-150' : ''}`}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* FAQs Section */}
      <section id="faqs" className="py-24 relative">
        <div className="absolute inset-0 bg-black/40 backdrop-blur-[1px]"></div>
        <div className="relative w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-5xl font-bold text-white mb-6">FAQs</h2>
          </div>

          <div className="space-y-6">
            {/* FAQ 1 */}
            <div className="group bg-gradient-to-br from-gray-700/30 to-gray-800/30 backdrop-blur-sm rounded-2xl border border-gray-600/30 overflow-hidden hover:border-blue-500/50 transition-all duration-500 hover:shadow-xl hover:shadow-blue-500/10">
              <button
                onClick={() => toggleFAQ(0)}
                className="w-full p-6 text-left flex justify-between items-center hover:bg-gray-700/20 transition-all duration-300"
              >
                <h3 className="text-xl font-semibold text-white group-hover:text-blue-400 transition-colors duration-300">
                  What all services does Qreate AI offer?
                </h3>
                <svg
                  className={`w-6 h-6 text-gray-400 transition-transform duration-300 ${openFAQ === 0 ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {openFAQ === 0 && (
                <div className="px-6 pt-2 pb-6 animate-fade-in">
                  <p className="text-gray-300 leading-relaxed">
                    We offer comprehensive AI/ML solutions including Computer Vision, Reinforcement Learning, ETL Pipelining, and Audio/Speech processing. Our development stack covers React frontends, FastAPI backends, and cloud deployment on GCP/AWS.
                  </p>
                </div>
              )}
            </div>

            {/* FAQ 2 */}
            <div className="group bg-gradient-to-br from-gray-700/30 to-gray-800/30 backdrop-blur-sm rounded-2xl border border-gray-600/30 overflow-hidden hover:border-purple-500/50 transition-all duration-500 hover:shadow-xl hover:shadow-purple-500/10">
              <button
                onClick={() => toggleFAQ(1)}
                className="w-full p-6 text-left flex justify-between items-center hover:bg-gray-700/20 transition-all duration-300"
              >
                <h3 className="text-xl font-semibold text-white group-hover:text-purple-400 transition-colors duration-300">
                  How long does a typical software development project take?
                </h3>
                <svg
                  className={`w-6 h-6 text-gray-400 transition-transform duration-300 ${openFAQ === 1 ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {openFAQ === 1 && (
                <div className="px-6 pt-2 pb-6 animate-fade-in">
                  <p className="text-gray-300 leading-relaxed">
                    Project timelines vary based on complexity and scope. Simple integrations can be completed in 2-4 weeks, while comprehensive AI applications typically take 8-16 weeks. We provide detailed timelines during our initial consultation.
                  </p>
                </div>
              )}
            </div>

            {/* FAQ 3 */}
            <div className="group bg-gradient-to-br from-gray-700/30 to-gray-800/30 backdrop-blur-sm rounded-2xl border border-gray-600/30 overflow-hidden hover:border-green-500/50 transition-all duration-500 hover:shadow-xl hover:shadow-green-500/10">
              <button
                onClick={() => toggleFAQ(2)}
                className="w-full p-6 text-left flex justify-between items-center hover:bg-gray-700/20 transition-all duration-300"
              >
                <h3 className="text-xl font-semibold text-white group-hover:text-green-400 transition-colors duration-300">
                  What does the broader process look like?
                </h3>
                <svg
                  className={`w-6 h-6 text-gray-400 transition-transform duration-300 ${openFAQ === 2 ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {openFAQ === 2 && (
                <div className="px-6 pt-2 pb-6 animate-fade-in">
                  <p className="text-gray-300 leading-relaxed">
                    Our process follows three key phases: Ideation (understanding your vision and goals), Development (designing and building your solution), and Launch (deploying and providing ongoing support). We maintain close collaboration throughout each phase.
                  </p>
                </div>
              )}
            </div>

            {/* FAQ 4 */}
            <div className="group bg-gradient-to-br from-gray-700/30 to-gray-800/30 backdrop-blur-sm rounded-2xl border border-gray-600/30 overflow-hidden hover:border-pink-500/50 transition-all duration-500 hover:shadow-xl hover:shadow-pink-500/10">
              <button
                onClick={() => toggleFAQ(3)}
                className="w-full p-6 text-left flex justify-between items-center hover:bg-gray-700/20 transition-all duration-300"
              >
                <h3 className="text-xl font-semibold text-white group-hover:text-pink-400 transition-colors duration-300">
                  What kind of quality assurance do we have? Do we offer maintenance?
                </h3>
                <svg
                  className={`w-6 h-6 text-gray-400 transition-transform duration-300 ${openFAQ === 3 ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {openFAQ === 3 && (
                <div className="px-6 pt-2 pb-6 animate-fade-in">
                  <p className="text-gray-300 leading-relaxed">
                    We implement comprehensive testing protocols and quality assurance measures throughout development. Yes, we provide ongoing maintenance and support services to ensure your AI solutions continue to perform optimally and stay updated with the latest technologies.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="text-white py-16 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/50 backdrop-blur-[1px]"></div>
        <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 z-10"></div>
        <div className="relative w-full px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-3xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">Qreate AI</h3>
            <p className="text-gray-400 mb-12 text-lg">AI-native product studio building fullstack AI/LLM apps</p>
            <div className="flex justify-center space-x-12 mb-8">
              <a href="#contact" className="text-gray-400 hover:text-white transition-all duration-300 hover:scale-110 relative group">
                Contact Us
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#about" className="text-gray-400 hover:text-white transition-all duration-300 hover:scale-110 relative group">
                About
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </a>
            </div>

            {/* Copyright */}
            <div className="border-t border-gray-700/50 pt-8">
              <p className="text-gray-500 text-sm">
                All rights reserved © Qreate AI Pvt. Ltd.
              </p>
            </div>
          </div>
        </div>
      </footer>

      {/* Floating Action Button */}
      <div className="fixed bottom-8 right-8 z-50">
        <button className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-4 rounded-full shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-110 animate-pulse-glow rocket-boost">
          <svg className="w-6 h-6 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
          </svg>
        </button>
      </div>
    </div>
  )
}

export default App
